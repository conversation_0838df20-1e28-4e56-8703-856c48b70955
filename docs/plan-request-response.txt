# curl -X POST "http://localhost:8000/api/v1/plan_task"
#  -H "Content-Type: application/json"
#  -H "user-token: your_user_token_here"
#  -d '{
#    "messages": [
#      {
#        "role": "user",
#        "content": "请帮我规划一下今天的任务"
#      }
#    ],
#    "juzi_data": {
#      "chatId": "123456",
#      "other_key": "other_value"
#    }
#  }'
POST http://localhost:8719/api/v1/plan_task
user-token: your_user_token_here
Content-Type: application/json

{
  "messages": [
    {
      "role": "user",
      "content": "外代销产品，请问给代销机构下发数据一般是几点？"
    }
  ],
  "juzi_data": {
    "chatId": "123456",
    "other_key": "other_value"
  }
}


POST http://localhost:8719/api/v1/plan_task

HTTP/1.1 200 OK
date: Thu, 14 Aug 2025 05:43:40 GMT
server: uvicorn
content-length: 1444
content-type: application/json

{
  "code": 0,
  "message": "success",
  "data": {
    "plans": [
      {
        "plugin_name": "general_plugins",
        "name": "context_qa",
        "description": "交互内容中存在和问题相关的信息时，请调用这个函数，根据交互内容或给出的文本，回答用户的问题",
        "variables": {
          "input": "外代销产品,请问给代销机构下发数据一般是几点?",
          "query": "外代销产品,请问给代销机构下发数据一般是几点?"
        }
      }
    ],
    "answer": "给代销机构下发数据的具体时间通常由合作双方的协议或系统对接规则决定，以下是一般情况下的参考信息：\n\n1. **常见时间段**  \n   - 多数机构选择在 **交易日收盘后（15:00-18:00）** 或 **晚间（20:00-24:00）** 下发数据，以确保当日交易数据完整。\n   - 部分机构可能采用 **次日凌晨（如2:00-5:00）** 批量处理模式。\n\n2. **关键影响因素**  \n   - 数据来源方的清算完成时间（如基金公司TA系统结算时间）。  \n   - 代销机构的系统接收窗口（需避开其系统维护时段）。  \n\n3. **建议确认方式**  \n   - 直接联系对接的业务经理或技术支持，获取协议中约定的具体时间表。  \n   - 查看双方签署的《代销服务协议》技术附件（通常包含数据交互时间条款）。  \n\n若您需要更具体的回答，请提供代销产品类型（如公募基金、信托等）或机构名称，可进一步帮助核实行业惯例。"
  }
}
Response file saved.
> 2025-08-14T134407.200.json
 
Response code: 200 (OK); Time: 25962ms (25 s 962 ms); Content length: 692 bytes (692 B)
  