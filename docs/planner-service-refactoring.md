# PlannerService 重构：从 Function Call 到 Prompt-Based 方法

## 概述

本次重构将 PlannerService 从使用 Spring AI 的 function call 机制改为基于 prompt 的方法。新的实现使用 `/src/main/resources/plan/skprompt.txt` 文件来生成函数调用计划，然后解析并执行相应的工具。

## 主要变更

### 1. 核心方法重构

#### 之前的实现
```java
// 调用 ChatClient 生成响应
String response = chatClient.prompt()
        .system(effectiveSystemPrompt)
        .user(input)
        .toolCallbacks(availableTools)  // 使用 function call
        .call()
        .content();
```

#### 新的实现
```java
// 调用 ChatClient 生成响应（不使用 toolCallbacks）
String response = chatClient.prompt()
        .system(effectiveSystemPrompt)
        .user(input)
        .call()
        .content();

// 解析响应并提取计划信息
PlanTaskResult result = parseResponse(response, input, sessionId);

// 如果有计划需要执行，执行工具调用
if (!result.getPlans().isEmpty()) {
    String toolResult = executeToolPlans(result.getPlans(), businessType);
    return toolResult != null ? toolResult : result.getAnswer();
}
```

### 2. 新增核心方法

#### `planWithPromptBasedApproach()`
- 基于 prompt 的规划方式
- 不使用 function call，而是通过 prompt 生成计划
- 然后解析并执行工具

#### `executeToolPlans()`
- 执行工具计划
- 将参数转换为 JSON 字符串
- 调用相应的工具并返回结果

### 3. 保持向后兼容

所有现有的公共接口都保持不变：
- `plan(String input, String businessType)`
- `planWithCustomPrompt(String input, String businessType, String systemPrompt)`
- `planTask(PlanTaskRequest request, String userToken)`

## 工作流程

### 1. 生成计划
1. 使用 `PromptTemplateManager` 渲染 `plan/skprompt.txt` 模板
2. 模板中包含可用工具列表和用户输入
3. AI 模型根据 prompt 生成 JSON 格式的计划

### 2. 解析计划
```json
{
  "plan": {
    "reason": "用户最新的要求是询价，我可以使用函数列表中的询价函数",
    "function": "order_plugin.inquiry_order_price",
    "parameters": {
      "input": "询：600105永鼎股份，虚值5%看涨，2个月，300万名义本金"
    }
  }
}
```

### 3. 执行工具
1. 根据 `function` 字段查找对应的工具
2. 将 `parameters` 转换为 JSON 字符串
3. 调用工具的 `call(String toolInput)` 方法
4. 返回工具执行结果

## 优势

### 1. 更好的控制
- 可以完全控制工具选择逻辑
- 可以自定义计划生成过程
- 更容易调试和监控

### 2. 灵活性
- 可以轻松修改 prompt 来改变行为
- 支持复杂的工具选择逻辑
- 可以添加自定义的计划验证

### 3. 透明度
- 所有的计划生成过程都是可见的
- 可以记录和分析 AI 的决策过程
- 更容易进行错误诊断

## 使用示例

### 基本用法
```java
@Autowired
private PlannerService plannerService;

// 使用新的基于 prompt 的方法
String result = plannerService.plan("询：600105永鼎股份，虚值5%看涨，2个月，300万名义本金", "plan");
```

### 自定义 prompt
```java
String customPrompt = "你是一个专业的金融助手...";
String result = plannerService.planWithCustomPrompt(input, "plan", customPrompt);
```

### 结构化请求
```java
PlanTaskRequest request = new PlanTaskRequest();
request.setMessages(messages);
request.setBusinessType("plan");

PlannerService.PlanTaskResult result = plannerService.planTask(request, userToken);
```

## 配置

### prompt 模板位置
- 文件路径：`src/main/resources/plan/skprompt.txt`
- 模板变量：
  - `{{ActionPlanner_Excluded.ListOfFunctions}}` - 可用工具列表
  - `{{ $input }}` - 用户输入

### 工具列表格式
```text
// 根据合约号执行订单的平仓、撤单、卖出操作
order_plugin.close_order
请求参数 "input": 客户的平仓文本.

// 根据订单信息查询订单价格.
order_plugin.inquiry_order_price
请求参数 "input": 客户的询价文本.
```

## 测试

新的实现包含了完整的测试覆盖：
- `PlannerServicePromptBasedTest` - 测试新的基于 prompt 的实现
- 所有现有测试继续通过，确保向后兼容性

## 迁移指南

对于现有代码，无需任何更改。新的实现完全向后兼容，所有现有的 API 调用都会自动使用新的基于 prompt 的方法。

如果需要自定义行为，可以：
1. 修改 `src/main/resources/plan/skprompt.txt` 文件
2. 使用 `planWithCustomPrompt()` 方法提供自定义 prompt
3. 调用 `reloadPromptTemplate()` 重新加载模板
