package com.phodal.semantic;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phodal.semantic.controller.dto.PlanInfo;
import com.phodal.semantic.controller.dto.PlanTaskRequest;
import com.phodal.semantic.model.DynamicModelService;
import com.phodal.semantic.template.PromptTemplateManager;
import com.phodal.semantic.tools.ToolLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 规划服务，负责处理用户请求并生成相应的执行计划
 */
@Service
public class PlannerService {

    private static final Logger logger = LoggerFactory.getLogger(PlannerService.class);
    private final DynamicModelService dynamicModelService;
    private final ToolLoader toolLoader;
    private final PromptTemplateManager templateManager;
    private final ObjectMapper objectMapper;

    public PlannerService(DynamicModelService dynamicModelService, ToolLoader toolLoader, PromptTemplateManager templateManager) {
        this.dynamicModelService = dynamicModelService;
        this.toolLoader = toolLoader;
        this.templateManager = templateManager;
        this.objectMapper = new ObjectMapper();
    }



    /**
     * 根据用户输入和业务类型生成执行计划
     * @param input 用户输入
     * @param businessType 业务类型
     * @return 执行计划结果
     */
    public String plan(String input, String businessType) {
        if (input == null || input.trim().isEmpty()) {
            return "请提供有效的输入内容。";
        }

        try {
            logger.info("Processing plan request for business: {}, input: {}", businessType, input);

            // 获取对应业务的 ChatClient
            ChatClient chatClient = dynamicModelService.getChatClient(businessType);
            if (chatClient == null) {
                chatClient = dynamicModelService.getDefaultChatClient();
            }

            if (chatClient == null) {
                logger.error("No ChatClient available");
                return "系统暂时不可用，请稍后重试。";
            }

            // 获取可用的工具回调
            List<ToolCallback> availableTools = toolLoader.getAllAvailableTools(businessType);
            logger.info("Loaded {} tools for business: {}", availableTools.size(), businessType);

            // 使用模板管理器渲染提示词
            String systemPrompt = templateManager.renderPlanTemplate(businessType, input);

            // 调用 ChatClient 生成响应
            String response = chatClient.prompt()
                    .system(systemPrompt)
                    .user(input)
                    .toolCallbacks(availableTools)
                    .call()
                    .content();

            logger.info("Plan generated successfully for business: {}", businessType);
            return response;

        } catch (Exception e) {
            logger.error("Failed to generate plan for input: {}", input, e);
            return "生成计划时发生错误：" + e.getMessage();
        }
    }



    /**
     * 处理特定业务的规划请求
     * @param input 用户输入
     * @param businessType 业务类型
     * @param systemPrompt 自定义系统提示词
     * @return 执行计划结果
     */
    public String planWithCustomPrompt(String input, String businessType, String systemPrompt) {
        if (input == null || input.trim().isEmpty()) {
            return "请提供有效的输入内容。";
        }

        try {
            logger.info("Processing custom plan request for business: {}", businessType);

            // 获取对应业务的 ChatClient
            ChatClient chatClient = dynamicModelService.getChatClient(businessType);
            if (chatClient == null) {
                chatClient = dynamicModelService.getDefaultChatClient();
            }

            if (chatClient == null) {
                logger.error("No ChatClient available");
                return "系统暂时不可用，请稍后重试。";
            }

            // 获取可用的工具回调
            List<ToolCallback> availableTools = toolLoader.getAllAvailableTools(businessType);

            // 使用自定义系统提示词或模板管理器渲染的提示词
            String effectiveSystemPrompt;
            if (systemPrompt != null) {
                effectiveSystemPrompt = systemPrompt;
            } else {
                effectiveSystemPrompt = templateManager.renderPlanTemplate(businessType, input);
            }

            // 调用 ChatClient 生成响应
            String response = chatClient.prompt()
                    .system(effectiveSystemPrompt)
                    .user(input)
                    .toolCallbacks(availableTools)
                    .call()
                    .content();

            logger.info("Custom plan generated successfully for business: {}", businessType);
            return response;

        } catch (Exception e) {
            logger.error("Failed to generate custom plan for input: {}", input, e);
            return "生成计划时发生错误：" + e.getMessage();
        }
    }

    /**
     * 获取可用的工具列表信息
     * @param businessType 业务类型
     * @return 工具列表描述
     */
    public String getAvailableTools(String businessType) {
        try {
            List<ToolCallback> tools = toolLoader.getAllAvailableTools(businessType);
            StringBuilder toolsInfo = new StringBuilder();
            toolsInfo.append("可用工具列表（业务类型：").append(businessType).append("）：\n\n");

            int index = 1;
            for (ToolCallback tool : tools) {
                toolsInfo.append(index++).append(". ");
                toolsInfo.append(tool.getToolDefinition().name()).append("\n");
                toolsInfo.append("   描述：").append(tool.getToolDefinition().description()).append("\n\n");
            }

            return toolsInfo.toString();
        } catch (Exception e) {
            logger.error("Failed to get available tools for business: {}", businessType, e);
            return "获取工具列表时发生错误：" + e.getMessage();
        }
    }

    /**
     * 重新加载提示词模板
     */
    public void reloadPromptTemplate() {
        templateManager.reloadTemplate("plan/skprompt.txt");
        logger.info("Prompt template reloaded");
    }

    /**
     * 处理结构化的规划任务请求
     * @param request 规划任务请求
     * @param userToken 用户令牌，用于日志标识
     * @return 规划结果，包含计划列表和答案
     */
    public PlanTaskResult planTask(PlanTaskRequest request, String userToken) {
        String sessionId = "SemanticAgent-" + (userToken != null ? userToken : "unknown");

        // 记录请求日志
        logger.info("messages={} juzi_data={} {}",
            request.getMessages(),
            request.getJuziData(),
            userToken != null ? userToken : "no-token");

        String input = request.getLastUserMessage();
        if (input == null || input.trim().isEmpty()) {
            logger.warn("{}:planTask() - No valid user message found in request", sessionId);
            return new PlanTaskResult(Collections.emptyList(), "请提供有效的用户消息。");
        }

        String businessType = request.getBusinessType() != null ? request.getBusinessType() : "plan";

        try {
            logger.info("{}:action_planner.py:create_plan_async()- Finding the best function for achieving the goal: 用户: {}",
                sessionId, input);

            long startTime = System.currentTimeMillis();

            // 获取对应业务的 ChatClient
            ChatClient chatClient = dynamicModelService.getChatClient(businessType);
            if (chatClient == null) {
                chatClient = dynamicModelService.getDefaultChatClient();
            }

            if (chatClient == null) {
                logger.error("{}:planTask() - No ChatClient available", sessionId);
                return new PlanTaskResult(Collections.emptyList(), "系统暂时不可用，请稍后重试。");
            }

            // 获取可用的工具回调
            List<ToolCallback> availableTools = toolLoader.getAllAvailableTools(businessType);
            logger.info("{}:planTask() - Loaded {} tools for business: {}", sessionId, availableTools.size(), businessType);

            // 使用自定义系统提示词或模板管理器渲染的提示词
            String effectiveSystemPrompt;
            if (request.getSystemPrompt() != null) {
                effectiveSystemPrompt = request.getSystemPrompt();
            } else {
                effectiveSystemPrompt = templateManager.renderPlanTemplate(businessType, input);
            }

            // 调用 ChatClient 生成响应
            String response = chatClient.prompt()
                    .system(effectiveSystemPrompt)
                    .user(input)
                    .toolCallbacks(availableTools)
                    .call()
                    .content();

            long planTime = System.currentTimeMillis() - startTime;
            logger.info("PLAN {}", planTime / 1000.0);

            // 解析响应并提取计划信息
            PlanTaskResult result = parseResponse(response, input, sessionId);

            long totalTime = System.currentTimeMillis() - startTime;
            logger.info("ANSWER {}", totalTime / 1000.0);
            logger.info("LASTPART {}", (totalTime - planTime) / 1000.0);

            return result;

        } catch (Exception e) {
            logger.error("{}:planTask() - Failed to generate plan for input: {}", sessionId, input, e);
            return new PlanTaskResult(Collections.emptyList(), "生成计划时发生错误：" + e.getMessage());
        }
    }

    /**
     * 解析AI响应，提取计划信息和最终答案
     */
    private PlanTaskResult parseResponse(String response, String input, String sessionId) {
        List<PlanInfo> plans = new ArrayList<>();
        String answer = response;

        try {
            // 尝试从响应中提取JSON计划信息
            Pattern jsonPattern = Pattern.compile("\\{\"plan\":\\s*\\{[^}]+\\}\\}");
            Matcher matcher = jsonPattern.matcher(response);

            if (matcher.find()) {
                String jsonStr = matcher.group();
                logger.info("{}:action_planner.py:create_plan_async()- Plan generated by ActionPlanner:\n{}", sessionId, jsonStr);

                try {
                    JsonNode planNode = objectMapper.readTree(jsonStr);
                    JsonNode plan = planNode.get("plan");

                    if (plan != null) {
                        String reason = plan.has("reason") ? plan.get("reason").asText() : "";
                        String function = plan.has("function") ? plan.get("function").asText() : "";
                        JsonNode parameters = plan.get("parameters");

                        logger.info("{}:action_planner.py:create_plan_async()- Python dictionary of plan generated by ActionPlanner:\n{}",
                            sessionId, planNode.toString());

                        logger.info("{}:action_planner.py:create_plan_async()- {{\"is_finished\": false, \"type\": \"plan\", \"step\": \"thought\", \"step_value\": \"{}\"}}",
                            sessionId, reason);

                        if (function.contains(".")) {
                            String[] parts = function.split("\\.", 2);
                            String pluginName = parts[0];
                            String functionName = parts[1];

                            logger.info("{}- ActionPlanner has picked {}. Reference to this function found in context:",
                                sessionId, function);

                            // 记录参数日志
                            if (parameters != null) {
                                parameters.fields().forEachRemaining(entry -> {
                                    logger.info("{}- Parameter {}: {}",
                                        sessionId, entry.getKey(), entry.getValue().asText());
                                });
                            }

                            logger.info("{}- {{\"is_finished\": false, \"type\": \"plan\", \"step\": \"action\", \"step_value\": \"{}\"}}",
                                sessionId, function);

                            logger.info("{}- {{\"is_finished\": false, \"type\": \"plan\", \"step\": \"action_input\", \"step_value\": \"{}\"}}",
                                sessionId, input);

                            // 创建计划信息
                            Map<String, Object> variables = new HashMap<>();
                            if (parameters != null) {
                                parameters.fields().forEachRemaining(entry -> {
                                    variables.put(entry.getKey(), entry.getValue().asText());
                                });
                            }

                            PlanInfo planInfo = new PlanInfo(pluginName, functionName, reason, variables);
                            plans.add(planInfo);
                        }
                    }
                } catch (JsonProcessingException e) {
                    logger.warn("{}:parseResponse() - Failed to parse plan JSON: {}", sessionId, e.getMessage());
                }
            }

            if (!response.trim().startsWith("{")) {
                logger.info("{}- {{\"is_finished\": false, \"type\": \"plan\", \"step\": \"action_output\", \"step_value\": \"{}\"}}",
                    sessionId, response.replace("\n", "\\n"));

                logger.info("{}- {{\"is_finished\": true, \"type\": \"\", \"step\": \"\", \"step_value\": \"\"}}", sessionId);
            }

        } catch (Exception e) {
            logger.warn("{}:parseResponse() - Error parsing response: {}", sessionId, e.getMessage());
        }

        return new PlanTaskResult(plans, answer);
    }

    /**
     * 规划任务结果类
     */
    public static class PlanTaskResult {
        private final List<PlanInfo> plans;
        private final String answer;

        public PlanTaskResult(List<PlanInfo> plans, String answer) {
            this.plans = plans;
            this.answer = answer;
        }

        public List<PlanInfo> getPlans() {
            return plans;
        }

        public String getAnswer() {
            return answer;
        }
    }
}
